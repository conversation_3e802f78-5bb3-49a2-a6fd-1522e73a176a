/* 国家绿色低碳全产业链服务平台 - 现代化样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', 'Se<PERSON>e UI', <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

/* 主题颜色变量 */
:root {
    --primary-green: #2d5a27;
    --secondary-green: #4a7c59;
    --light-green: #8bc34a;
    --accent-blue: #1976d2;
    --light-blue: #e3f2fd;
    --warning-orange: #ff9800;
    --success-green: #4caf50;
    --error-red: #f44336;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --border-color: #e0e0e0;
    --shadow: 0 4px 20px rgba(0,0,0,0.1);
    --shadow-hover: 0 8px 30px rgba(0,0,0,0.15);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-green: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
}

/* 头部导航 */
.header {
    background: var(--gradient-green);
    color: white;
    padding: 0;
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header .container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    padding: 15px 0;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo i {
    font-size: 28px;
    margin-right: 10px;
    color: var(--light-green);
    animation: pulse 2s infinite;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 15px 0;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    color: var(--light-green);
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--light-green);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--light-green);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* 主要内容区域 */
main {
    margin-top: 70px;
    min-height: calc(100vh - 140px);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero 区域 */
.hero {
    background: var(--gradient-primary);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto 40px;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease-out;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-search {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out 0.6s both;
}

/* 卡片样式 */
.card {
    background: var(--gradient-card);
    border-radius: 16px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    background: var(--gradient-green);
    color: white;
    padding: 25px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.card-header i {
    font-size: 20px;
    opacity: 0.9;
}

.card-body {
    padding: 25px;
    background: white;
}

/* 统计数据区域 */
.stats-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.stats-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--text-dark);
    font-weight: 700;
}

.stats-section h2 i {
    color: var(--primary-green);
    margin-right: 15px;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 0 auto;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.stat-content {
    margin-bottom: 15px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-green);
    display: block;
    margin-bottom: 8px;
}

.stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
    font-weight: 500;
}

.stat-growth {
    color: var(--success-green);
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-growth i {
    margin-right: 5px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    margin: 5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--accent-blue);
    color: var(--accent-blue);
}

.btn-outline:hover {
    background: var(--accent-blue);
    color: white;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* 搜索框样式 */
.search-container {
    position: relative;
    margin-bottom: 20px;
}

.search-input, .ai-search-input {
    width: 100%;
    padding: 18px 60px 18px 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.search-input:focus, .ai-search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
    background: white;
}

.search-btn {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 18px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.search-suggestions-area {
    margin-top: 15px;
}

/* AI 功能展示区域 */
.ai-features {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.ai-features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--text-dark);
    font-weight: 700;
}

.ai-features h2 i {
    color: var(--primary-green);
    margin-right: 15px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 25px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
    font-weight: 600;
}

.feature-card p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.feature-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.feature-stats span {
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 600;
}

/* 监控面板 */
.monitoring-panel {
    padding: 80px 0;
    background: white;
}

.monitoring-panel h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--text-dark);
    font-weight: 700;
}

.monitoring-panel h2 i {
    color: var(--primary-green);
    margin-right: 15px;
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.monitor-card {
    background: var(--gradient-card);
    padding: 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.monitor-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.monitor-card h4 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.1rem;
    font-weight: 600;
}

.monitor-card h4 i {
    margin-right: 10px;
    color: var(--primary-green);
}

.monitor-value {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 15px;
}

.monitor-value .value {
    font-size: 2.5rem;
    font-weight: 700;
}

.monitor-value .unit {
    font-size: 1rem;
    color: var(--text-light);
}

.monitor-value .status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.monitor-value.success .value {
    color: var(--success-green);
}

.monitor-value.success .status {
    background: #e8f5e8;
    color: var(--success-green);
}

.monitor-value.warning .value {
    color: var(--warning-orange);
}

.monitor-value.warning .status {
    background: #fff3e0;
    color: var(--warning-orange);
}

.monitor-trend {
    color: var(--text-light);
    font-size: 0.9rem;
}

.monitor-trend i {
    margin-right: 5px;
}

/* 最新动态 */
.latest-updates {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.latest-updates h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--text-dark);
    font-weight: 700;
}

.latest-updates h2 i {
    color: var(--primary-green);
    margin-right: 15px;
}

.updates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.update-card {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.update-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.update-type {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.update-type.policy {
    background: #e3f2fd;
    color: var(--accent-blue);
}

.update-type.finance {
    background: #e8f5e8;
    color: var(--success-green);
}

.update-type.tech {
    background: #fff3e0;
    color: var(--warning-orange);
}

.update-card h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

.update-card p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.update-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.update-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 快速入口 */
.quick-access {
    padding: 80px 0;
    background: white;
}

.quick-access h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--text-dark);
    font-weight: 700;
}

.quick-access h2 i {
    color: var(--primary-green);
    margin-right: 15px;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.access-card {
    background: var(--gradient-card);
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.access-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
    text-decoration: none;
    color: inherit;
}

.access-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.access-card:hover .access-icon {
    transform: scale(1.1) rotate(5deg);
}

.access-card h4 {
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 1.2rem;
    font-weight: 600;
}

.access-card p {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 页脚 */
.footer {
    background: var(--gradient-green);
    color: white;
    padding: 60px 0 30px;
    margin-top: 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--light-green);
    font-weight: 600;
}

.footer-section p {
    line-height: 1.6;
    opacity: 0.9;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    padding: 5px 0;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.footer-section ul li:hover {
    opacity: 1;
    color: var(--light-green);
}

.footer-section ul li::before {
    content: '▸';
    margin-right: 8px;
    color: var(--light-green);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0.8;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .header .container {
        padding: 0 15px;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid,
    .features-grid,
    .monitoring-grid,
    .updates-grid,
    .access-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .container {
        padding: 0 15px;
    }

    .stats-section,
    .ai-features,
    .monitoring-panel,
    .latest-updates,
    .quick-access {
        padding: 60px 0;
    }

    .stats-section h2,
    .ai-features h2,
    .monitoring-panel h2,
    .latest-updates h2,
    .quick-access h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .feature-card,
    .monitor-card,
    .update-card,
    .access-card {
        padding: 25px 20px;
    }
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    padding: 60px 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-area:hover {
    border-color: var(--accent-blue);
    background: var(--light-blue);
}

.upload-area:hover::before {
    opacity: 0.05;
}

.upload-area.dragover {
    border-color: var(--light-green);
    background: #e8f5e8;
}

/* 进度条 */
.progress {
    background: #e0e0e0;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    background: var(--gradient-primary);
    height: 100%;
    transition: width 0.3s ease;
}

/* 标签样式 */
.tag {
    display: inline-block;
    background: var(--light-blue);
    color: var(--accent-blue);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin: 2px;
    transition: all 0.3s ease;
}

.tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.tag-green {
    background: #e8f5e8;
    color: var(--primary-green);
}

/* AI 功能突出显示 */
.ai-feature {
    background: var(--gradient-primary);
    color: white;
    padding: 20px;
    border-radius: 16px;
    margin: 15px 0;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ai-feature::before {
    content: '🤖';
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    opacity: 0.7;
}

.ai-feature:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.ai-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.ai-badge:hover {
    transform: scale(1.05);
    cursor: pointer;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: pointer;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-green);
}